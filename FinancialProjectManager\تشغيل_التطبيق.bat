@echo off
title Financial Project Manager

echo ========================================
echo      Financial Project Manager
echo ========================================
echo.

echo Starting the application...
echo.

REM Check for standalone executable first
if exist "bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe" (
    echo Found standalone executable
    "bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe"
    goto :end
)

REM Check for Debug executable
if exist "bin\Debug\net8.0-windows\FinancialProjectManager.exe" (
    echo Found Debug executable
    "bin\Debug\net8.0-windows\FinancialProjectManager.exe"
    goto :end
)

REM Try dotnet run as last resort
echo No executable found, trying dotnet run...
dotnet run

:end
echo.
echo Application started!
echo.
pause
