@echo off
chcp 65001 > nul
title نظام إدارة المشاريع المالية

echo ========================================
echo      نظام إدارة المشاريع المالية
echo    Financial Project Manager
echo ========================================
echo.

echo جاري تشغيل التطبيق...
echo Starting the application...
echo.

REM تشغيل الملف التنفيذي المستقل
if exist "bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe" (
    echo تم العثور على الملف التنفيذي المستقل
    echo Found standalone executable
    start "" "bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe"
    goto :end
)

REM تشغيل ملف Debug إذا لم يوجد Release
if exist "bin\Debug\net8.0-windows\FinancialProjectManager.exe" (
    echo تم العثور على ملف Debug
    echo Found Debug executable
    start "" "bin\Debug\net8.0-windows\FinancialProjectManager.exe"
    goto :end
)

REM تشغيل باستخدام dotnet run كحل أخير
echo لم يتم العثور على ملف تنفيذي، جاري التشغيل من الكود المصدري...
echo No executable found, running from source code...
dotnet run

:end
echo.
echo تم تشغيل التطبيق بنجاح!
echo Application started successfully!
echo.
pause
