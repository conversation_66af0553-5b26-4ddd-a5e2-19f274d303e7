using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialProjectManager.Models
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime ArrivalDate { get; set; }
        
        public DateTime? SignatureDate { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountInUSD { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal ExchangeRate { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountInEGP { get; set; }
        
        [Required]
        public InvoiceType InvoiceType { get; set; }
        
        [Required]
        public PaymentStatus PaymentStatus { get; set; }
        
        public int? CommitmentId { get; set; }
        
        public string? Notes { get; set; }
        
        public string? PdfFilePath { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedDate { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
        
        [ForeignKey("CommitmentId")]
        public virtual Commitment? Commitment { get; set; }
        
        public virtual ICollection<LetterInvoice> LetterInvoices { get; set; } = new List<LetterInvoice>();
    }
    
    public enum InvoiceType
    {
        Hardware = 1,
        Software = 2,
        Service = 3
    }
    
    public enum PaymentStatus
    {
        Pending = 1,
        PartiallyPaid = 2,
        Paid = 3,
        Cancelled = 4
    }
}
