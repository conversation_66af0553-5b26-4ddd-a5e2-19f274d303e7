<Window x:Class="FinancialProjectManager.Views.ProjectDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل المشروع" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen" WindowState="Maximized"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
        
        <Style x:Key="SummaryCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#ECF0F1">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock x:Name="ProjectHeaderTextBlock" Text="تفاصيل المشروع" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Financial Summary -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,20,20,10">
            <StackPanel>
                <TextBlock Text="الملخص المالي" Style="{StaticResource HeaderStyle}"/>
                <UniformGrid Rows="1" Columns="4">
                    <Border Style="{StaticResource SummaryCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💼" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="أمر الشراء" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="POAmountTextBlock" Text="0 جنيه" FontSize="16" 
                                      Foreground="#27AE60" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Style="{StaticResource SummaryCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="🔧" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="المهمات (هاردوير + سوفتوير)" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="HardwareSoftwareSpentTextBlock" Text="0 جنيه" FontSize="16" 
                                      Foreground="#3498DB" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Style="{StaticResource SummaryCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⚙️" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="الخدمات" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="ServicesSpentTextBlock" Text="0 جنيه" FontSize="16" 
                                      Foreground="#9B59B6" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Style="{StaticResource SummaryCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="المتبقي" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="RemainingAmountTextBlock" Text="0 جنيه" FontSize="16" 
                                      Foreground="#E74C3C" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>
            </StackPanel>
        </Border>
        
        <!-- Tabs -->
        <TabControl Grid.Row="2" Margin="20,10,20,20" Background="White">
            <!-- Invoices Tab -->
            <TabItem Header="الفواتير" FontSize="16" FontWeight="Bold">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                        <Button Content="إضافة فاتورة" Background="#27AE60" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="AddInvoiceButton_Click"/>
                        <Button Content="تحديث" Background="#3498DB" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="RefreshInvoicesButton_Click"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid" AutoGenerateColumns="False" 
                             CanUserAddRows="False" CanUserDeleteRows="False" 
                             GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                             AlternatingRowBackground="#F8F9FA">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الوصول" Binding="{Binding ArrivalDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="تاريخ التوقيع" Binding="{Binding SignatureDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ بالدولار" Binding="{Binding AmountInUSD, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="سعر الصرف" Binding="{Binding ExchangeRate, StringFormat=N4}" Width="80"/>
                            <DataGridTextColumn Header="المبلغ بالجنيه" Binding="{Binding AmountInEGP, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding InvoiceType}" Width="80"/>
                            <DataGridTextColumn Header="حالة الصرف" Binding="{Binding PaymentStatus}" Width="100"/>
                            <DataGridTextColumn Header="رقم الارتباط" Binding="{Binding Commitment.CommitmentNumber}" Width="100"/>
                            <DataGridTextColumn Header="ملحوظات" Binding="{Binding Notes}" Width="150"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل" Background="#F39C12" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="EditInvoiceButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="حذف" Background="#E74C3C" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="DeleteInvoiceButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="فتح ملف" Background="#9B59B6" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="OpenInvoiceFileButton_Click" Tag="{Binding PdfFilePath}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- Commitments Tab -->
            <TabItem Header="الارتباطات" FontSize="16" FontWeight="Bold">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                        <Button Content="إضافة ارتباط" Background="#27AE60" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="AddCommitmentButton_Click"/>
                        <Button Content="تحديث" Background="#3498DB" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="RefreshCommitmentsButton_Click"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="1" x:Name="CommitmentsDataGrid" AutoGenerateColumns="False" 
                             CanUserAddRows="False" CanUserDeleteRows="False" 
                             GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                             AlternatingRowBackground="#F8F9FA">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الارتباط" Binding="{Binding CommitmentNumber}" Width="120"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding CommitmentType}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="حالة الإمضاء" Binding="{Binding SignatureStatus}" Width="100"/>
                            <DataGridTextColumn Header="المصروف" Binding="{Binding TotalSpent, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="250">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل" Background="#F39C12" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="EditCommitmentButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="حذف" Background="#E74C3C" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="DeleteCommitmentButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="فتح ملف" Background="#9B59B6" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="OpenCommitmentFileButton_Click" Tag="{Binding PdfFilePath}"/>
                                            <Button Content="الفواتير" Background="#34495E" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="ViewCommitmentInvoicesButton_Click" Tag="{Binding Id}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- Letters Tab -->
            <TabItem Header="الخطابات" FontSize="16" FontWeight="Bold">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                        <Button Content="إضافة خطاب" Background="#27AE60" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="AddLetterButton_Click"/>
                        <Button Content="تحديث" Background="#3498DB" Foreground="White" 
                               Style="{StaticResource ActionButtonStyle}" Click="RefreshLettersButton_Click"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="1" x:Name="LettersDataGrid" AutoGenerateColumns="False" 
                             CanUserAddRows="False" CanUserDeleteRows="False" 
                             GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                             AlternatingRowBackground="#F8F9FA">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الخطاب" Binding="{Binding LetterNumber}" Width="120"/>
                            <DataGridTextColumn Header="الموضوع" Binding="{Binding Subject}" Width="200"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل" Background="#F39C12" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="EditLetterButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="حذف" Background="#E74C3C" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="DeleteLetterButton_Click" Tag="{Binding Id}"/>
                                            <Button Content="فتح ملف" Background="#9B59B6" Foreground="White" 
                                                   Style="{StaticResource ActionButtonStyle}" 
                                                   Click="OpenLetterFileButton_Click" Tag="{Binding PdfFilePath}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
