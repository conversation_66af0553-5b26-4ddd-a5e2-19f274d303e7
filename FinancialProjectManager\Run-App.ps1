# Financial Project Manager - PowerShell Launcher
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "      Financial Project Manager" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Starting the application..." -ForegroundColor Yellow
Write-Host ""

# Check for standalone executable first
$standaloneExe = "bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe"
if (Test-Path $standaloneExe) {
    Write-Host "Found standalone executable" -ForegroundColor Green
    Start-Process $standaloneExe
    Write-Host "Application started successfully!" -ForegroundColor Green
    exit
}

# Check for Debug executable
$debugExe = "bin\Debug\net8.0-windows\FinancialProjectManager.exe"
if (Test-Path $debugExe) {
    Write-Host "Found Debug executable" -ForegroundColor Green
    Start-Process $debugExe
    Write-Host "Application started successfully!" -ForegroundColor Green
    exit
}

# Try dotnet run as last resort
Write-Host "No executable found, trying dotnet run..." -ForegroundColor Yellow
try {
    dotnet run
    Write-Host "Application started successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error starting application: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
