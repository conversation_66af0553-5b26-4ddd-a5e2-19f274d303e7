<Window x:Class="FinancialProjectManager.Views.EditInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل الفاتورة" Height="600" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="تعديل الفاتورة" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,30"/>
        
        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <Label Content="رقم الفاتورة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="InvoiceNumberTextBox" Style="{StaticResource TextBoxStyle}"/>
                
                <Label Content="تاريخ الوصول:" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="ArrivalDatePicker" Style="{StaticResource TextBoxStyle}"/>
                
                <Label Content="تاريخ التوقيع (اختياري):" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="SignatureDatePicker" Style="{StaticResource TextBoxStyle}"/>
                
                <Label Content="المبلغ بالدولار:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="AmountInUSDTextBox" Style="{StaticResource TextBoxStyle}" 
                         TextChanged="AmountInUSDTextBox_TextChanged"/>
                
                <Label Content="سعر الصرف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="ExchangeRateTextBox" Style="{StaticResource TextBoxStyle}" 
                         TextChanged="ExchangeRateTextBox_TextChanged"/>
                
                <Label Content="المبلغ بالجنيه:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="AmountInEGPTextBox" Style="{StaticResource TextBoxStyle}" IsReadOnly="True" 
                         Background="#F8F9FA"/>
                
                <Label Content="نوع الفاتورة:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="InvoiceTypeComboBox" Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="هاردوير" Tag="Hardware"/>
                    <ComboBoxItem Content="سوفتوير" Tag="Software"/>
                    <ComboBoxItem Content="خدمة" Tag="Service"/>
                </ComboBox>
                
                <Label Content="حالة الصرف:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="PaymentStatusComboBox" Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="في الانتظار" Tag="Pending"/>
                    <ComboBoxItem Content="مدفوع جزئياً" Tag="PartiallyPaid"/>
                    <ComboBoxItem Content="مدفوع كاملاً" Tag="Paid"/>
                    <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                </ComboBox>
                
                <Label Content="ملحوظات (اختياري):" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="NotesTextBox" Style="{StaticResource TextBoxStyle}" 
                         Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto"/>
                
                <Label Content="ملف PDF (اختياري):" Style="{StaticResource LabelStyle}"/>
                <StackPanel Orientation="Horizontal">
                    <TextBox x:Name="PdfFilePathTextBox" Style="{StaticResource TextBoxStyle}" 
                             Width="300" IsReadOnly="True" Background="#F8F9FA"/>
                    <Button Content="اختيار ملف" Background="#3498DB" Foreground="White" 
                           Style="{StaticResource ButtonStyle}" Click="SelectPdfFileButton_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Content="حفظ التغييرات" Style="{StaticResource ButtonStyle}" 
                    Background="#27AE60" Foreground="White" 
                    Click="SaveButton_Click"/>
            <Button Content="إلغاء" Style="{StaticResource ButtonStyle}" 
                    Background="#E74C3C" Foreground="White" 
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
