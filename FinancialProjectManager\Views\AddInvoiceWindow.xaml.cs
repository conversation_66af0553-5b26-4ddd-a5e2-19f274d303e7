using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using FinancialProjectManager.Models;
using FinancialProjectManager.Services;

namespace FinancialProjectManager.Views
{
    public partial class AddInvoiceWindow : Window
    {
        private readonly int _projectId;
        private readonly InvoiceService _invoiceService;
        private readonly FileService _fileService;
        private string? _selectedPdfPath;

        public AddInvoiceWindow(int projectId, InvoiceService invoiceService, FileService fileService)
        {
            InitializeComponent();
            _projectId = projectId;
            _invoiceService = invoiceService;
            _fileService = fileService;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Set default values
            ArrivalDatePicker.SelectedDate = DateTime.Today;
            InvoiceTypeComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = 0;
            ExchangeRateTextBox.Text = "30.0"; // Default exchange rate
        }

        private void AmountInUSDTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateAmountInEGP();
        }

        private void ExchangeRateTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateAmountInEGP();
        }

        private void CalculateAmountInEGP()
        {
            if (decimal.TryParse(AmountInUSDTextBox.Text, out decimal usdAmount) &&
                decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
            {
                var egpAmount = usdAmount * exchangeRate;
                AmountInEGPTextBox.Text = egpAmount.ToString("N2");
            }
            else
            {
                AmountInEGPTextBox.Text = "0.00";
            }
        }

        private void SelectPdfFileButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف PDF",
                Filter = "PDF Files (*.pdf)|*.pdf",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedPdfPath = openFileDialog.FileName;
                PdfFilePathTextBox.Text = System.IO.Path.GetFileName(_selectedPdfPath);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceNumberTextBox.Focus();
                    return;
                }

                if (!ArrivalDatePicker.SelectedDate.HasValue)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الوصول", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ArrivalDatePicker.Focus();
                    return;
                }

                if (!decimal.TryParse(AmountInUSDTextBox.Text, out decimal usdAmount) || usdAmount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح بالدولار", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    AmountInUSDTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate) || exchangeRate <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صرف صحيح", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ExchangeRateTextBox.Focus();
                    return;
                }

                if (InvoiceTypeComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الفاتورة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceTypeComboBox.Focus();
                    return;
                }

                if (PaymentStatusComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار حالة الصرف", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    PaymentStatusComboBox.Focus();
                    return;
                }

                // Check if invoice number is unique
                var isUnique = await _invoiceService.IsInvoiceNumberUniqueAsync(InvoiceNumberTextBox.Text.Trim());
                if (!isUnique)
                {
                    MessageBox.Show("رقم الفاتورة موجود بالفعل، يرجى اختيار رقم آخر", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceNumberTextBox.Focus();
                    return;
                }

                // Save PDF file if selected
                string? savedPdfPath = null;
                if (!string.IsNullOrEmpty(_selectedPdfPath))
                {
                    savedPdfPath = await _fileService.SaveInvoiceFileAsync(InvoiceNumberTextBox.Text.Trim(), _selectedPdfPath);
                }

                // Parse enum values
                var invoiceTypeItem = (ComboBoxItem)InvoiceTypeComboBox.SelectedItem;
                var invoiceType = Enum.Parse<InvoiceType>(invoiceTypeItem.Tag.ToString()!);

                var paymentStatusItem = (ComboBoxItem)PaymentStatusComboBox.SelectedItem;
                var paymentStatus = Enum.Parse<PaymentStatus>(paymentStatusItem.Tag.ToString()!);

                // Create new invoice
                var newInvoice = new Invoice
                {
                    InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
                    ArrivalDate = ArrivalDatePicker.SelectedDate.Value,
                    SignatureDate = SignatureDatePicker.SelectedDate,
                    AmountInUSD = usdAmount,
                    ExchangeRate = exchangeRate,
                    AmountInEGP = usdAmount * exchangeRate,
                    InvoiceType = invoiceType,
                    PaymentStatus = paymentStatus,
                    Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim(),
                    PdfFilePath = savedPdfPath,
                    ProjectId = _projectId,
                    CreatedDate = DateTime.Now
                };

                await _invoiceService.AddAsync(newInvoice);

                MessageBox.Show("تم إضافة الفاتورة بنجاح", "نجح الحفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void NumericTextBox_PreviewTextInput(object sender, System.Windows.Input.TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            if (!char.IsDigit(e.Text, 0) && e.Text != ".")
            {
                e.Handled = true;
            }
        }
    }
}
