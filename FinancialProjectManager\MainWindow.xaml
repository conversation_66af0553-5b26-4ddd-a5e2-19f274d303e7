﻿<Window x:Class="FinancialProjectManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FinancialProjectManager"
        mc:Ignorable="d"
        Title="Financial Project Manager" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" WindowState="Maximized"
        FlowDirection="LeftToRight">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ProjectCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#3498DB"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#ECF0F1">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💼" FontSize="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
                <TextBlock Text="Financial Project Manager"
                          FontSize="28" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Statistics Section -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,20,20,10">
            <StackPanel>
                <TextBlock Text="General Statistics" Style="{StaticResource SubHeaderTextStyle}"/>
                <UniformGrid Rows="1" Columns="4">
                    <Border Style="{StaticResource StatCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="Total Purchase Orders" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalPOTextBlock" Text="$0.00" FontSize="20"
                                      Foreground="#27AE60" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{StaticResource StatCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="Total Spent" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalSpentTextBlock" Text="$0.00" FontSize="20"
                                      Foreground="#E74C3C" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{StaticResource StatCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💵" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="Total Remaining" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalRemainingTextBlock" Text="$0.00" FontSize="20"
                                      Foreground="#F39C12" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{StaticResource StatCardStyle}">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📁" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="Project Count" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="ProjectCountTextBlock" Text="0" FontSize="20"
                                      Foreground="#9B59B6" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>
            </StackPanel>
        </Border>

        <!-- Projects Section -->
        <ScrollViewer Grid.Row="2" Margin="20,10,20,20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <DockPanel Margin="0,0,0,20">
                    <TextBlock Text="Projects" Style="{StaticResource SubHeaderTextStyle}" DockPanel.Dock="Left"/>
                    <Button Content="Add New Project" Style="{StaticResource ActionButtonStyle}"
                           HorizontalAlignment="Right" DockPanel.Dock="Right"
                           Click="AddProjectButton_Click"/>
                </DockPanel>

                <ItemsControl x:Name="ProjectsItemsControl">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="2"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>

                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Style="{StaticResource ProjectCardStyle}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Project Header -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                        <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding Name}" FontSize="20" FontWeight="Bold"
                                                  Foreground="#2C3E50"/>
                                        <TextBlock Text="{Binding Code, StringFormat={}({0})}" FontSize="16"
                                                  Foreground="#7F8C8D" Margin="10,0,0,0"/>
                                    </StackPanel>

                                    <!-- Financial Summary -->
                                    <Grid Grid.Row="1" Margin="0,0,0,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="Purchase Order:" FontWeight="Bold" FontSize="12"/>
                                            <TextBlock Text="{Binding PurchaseOrderAmount, StringFormat={}{0:C}}"
                                                      FontSize="14" Foreground="#27AE60"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="Spent:" FontWeight="Bold" FontSize="12"/>
                                            <TextBlock Text="{Binding TotalSpent, StringFormat={}{0:C}}"
                                                      FontSize="14" Foreground="#E74C3C"/>
                                        </StackPanel>
                                    </Grid>

                                    <StackPanel Grid.Row="2" Margin="0,0,0,15">
                                        <TextBlock Text="Remaining:" FontWeight="Bold" FontSize="12"/>
                                        <TextBlock Text="{Binding RemainingAmount, StringFormat={}{0:C}}"
                                                  FontSize="14" Foreground="#F39C12"/>
                                    </StackPanel>

                                    <!-- Progress Bar -->
                                    <StackPanel Grid.Row="3" Margin="0,0,0,15">
                                        <TextBlock Text="Progress:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                        <ProgressBar Value="{Binding SpentPercentage}" Maximum="100" Height="20"
                                                    Background="#ECF0F1" Foreground="#3498DB"/>
                                        <TextBlock Text="{Binding SpentPercentage, StringFormat={}{0:F1}%}"
                                                  HorizontalAlignment="Center" FontSize="12" Margin="0,5,0,0"/>
                                    </StackPanel>

                                    <!-- Action Button -->
                                    <Button Grid.Row="4" Content="View Details"
                                           Style="{StaticResource ActionButtonStyle}"
                                           Click="ViewProjectDetails_Click"
                                           Tag="{Binding Id}"/>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
