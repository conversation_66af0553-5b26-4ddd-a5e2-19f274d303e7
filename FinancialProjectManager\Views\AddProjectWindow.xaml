<Window x:Class="FinancialProjectManager.Views.AddProjectWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مشروع جديد" Height="400" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="إضافة مشروع جديد" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,30"/>
        
        <!-- Form -->
        <StackPanel Grid.Row="1">
            <Label Content="اسم المشروع:" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="ProjectNameTextBox" Style="{StaticResource TextBoxStyle}"/>
            
            <Label Content="رمز المشروع:" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="ProjectCodeTextBox" Style="{StaticResource TextBoxStyle}" 
                     MaxLength="10" CharacterCasing="Upper"/>
            
            <Label Content="مبلغ أمر الشراء (بالجنيه):" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="PurchaseOrderAmountTextBox" Style="{StaticResource TextBoxStyle}"/>
            
            <Label Content="الوصف (اختياري):" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="DescriptionTextBox" Style="{StaticResource TextBoxStyle}" 
                     Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto"/>
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Content="حفظ" Style="{StaticResource ButtonStyle}" 
                    Background="#27AE60" Foreground="White" 
                    Click="SaveButton_Click"/>
            <Button Content="إلغاء" Style="{StaticResource ButtonStyle}" 
                    Background="#E74C3C" Foreground="White" 
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
