﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using FinancialProjectManager.Data;
using FinancialProjectManager.Services;
using FinancialProjectManager.Models;
using FinancialProjectManager.Views;
using Microsoft.EntityFrameworkCore;

namespace FinancialProjectManager;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly ProjectService _projectService;
    private readonly FileService _fileService;

    public MainWindow()
    {
        try
        {
            InitializeComponent();

            // Initialize services step by step
            _fileService = new FileService();

            var context = new FinancialDbContext();
            _projectService = new ProjectService(context);

            // Initialize database and load data
            Loaded += MainWindow_Loaded;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing main window: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Environment.Exit(1);
        }
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            await InitializeDatabaseAsync();
            await LoadDataAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var context = new FinancialDbContext();
            await context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Database initialization error: {ex.Message}", "Error",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadDataAsync()
    {
        try
        {
            // Load projects
            var projects = await _projectService.GetAllAsync();

            // Check if UI elements exist before setting them
            if (ProjectsItemsControl != null)
                ProjectsItemsControl.ItemsSource = projects;

            // Load statistics
            var summary = await _projectService.GetProjectSummaryAsync();

            if (TotalPOTextBlock != null)
                TotalPOTextBlock.Text = $"{summary["TotalPO"]:C}";
            if (TotalSpentTextBlock != null)
                TotalSpentTextBlock.Text = $"{summary["TotalSpent"]:C}";
            if (TotalRemainingTextBlock != null)
                TotalRemainingTextBlock.Text = $"{summary["TotalRemaining"]:C}";
            if (ProjectCountTextBlock != null)
                ProjectCountTextBlock.Text = summary["ProjectCount"].ToString();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void AddProjectButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addProjectWindow = new AddProjectWindow(_projectService);
            if (addProjectWindow.ShowDialog() == true)
            {
                // Refresh data after adding new project
                await LoadDataAsync();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error opening add project window: {ex.Message}", "Error",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ViewProjectDetails_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.Tag is int projectId)
            {
                var projectDetailsWindow = new ProjectDetailsWindow(projectId, _projectService, _fileService);
                projectDetailsWindow.Show();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تفاصيل المشروع: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        base.OnClosed(e);
        Application.Current.Shutdown();
    }
}