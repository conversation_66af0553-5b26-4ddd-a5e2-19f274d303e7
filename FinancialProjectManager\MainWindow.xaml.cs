using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using FinancialProjectManager.Data;
using FinancialProjectManager.Services;
using FinancialProjectManager.Models;
using FinancialProjectManager.Views;
using Microsoft.EntityFrameworkCore;

namespace FinancialProjectManager;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly ProjectService _projectService;
    private readonly FileService _fileService;

    public MainWindow()
    {
        InitializeComponent();

        // Initialize services
        var context = new FinancialDbContext();
        _projectService = new ProjectService(context);
        _fileService = new FileService();

        // Initialize database and load data
        Loaded += MainWindow_Loaded;
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        await InitializeDatabaseAsync();
        await LoadDataAsync();
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var context = new FinancialDbContext();
            await context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadDataAsync()
    {
        try
        {
            // Load projects
            var projects = await _projectService.GetAllAsync();
            ProjectsItemsControl.ItemsSource = projects;

            // Load statistics
            var summary = await _projectService.GetProjectSummaryAsync();
            TotalPOTextBlock.Text = $"{summary["TotalPO"]:N0} جنيه";
            TotalSpentTextBlock.Text = $"{summary["TotalSpent"]:N0} جنيه";
            TotalRemainingTextBlock.Text = $"{summary["TotalRemaining"]:N0} جنيه";
            ProjectCountTextBlock.Text = summary["ProjectCount"].ToString();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void AddProjectButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addProjectWindow = new AddProjectWindow(_projectService);
            if (addProjectWindow.ShowDialog() == true)
            {
                // Refresh data after adding new project
                LoadDataAsync();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة المشروع: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ViewProjectDetails_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.Tag is int projectId)
            {
                var projectDetailsWindow = new ProjectDetailsWindow(projectId, _projectService, _fileService);
                projectDetailsWindow.Show();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تفاصيل المشروع: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        base.OnClosed(e);
        Application.Current.Shutdown();
    }
}