using System.Windows;
using FinancialProjectManager.Models;
using FinancialProjectManager.Services;

namespace FinancialProjectManager.Views
{
    public partial class AddProjectWindow : Window
    {
        private readonly ProjectService _projectService;

        public AddProjectWindow(ProjectService projectService)
        {
            InitializeComponent();
            _projectService = projectService;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(ProjectNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المشروع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(ProjectCodeTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رمز المشروع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectCodeTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(PurchaseOrderAmountTextBox.Text, out decimal poAmount) || poAmount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح لأمر الشراء", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    PurchaseOrderAmountTextBox.Focus();
                    return;
                }

                // Check if project code already exists
                var existingProject = await _projectService.GetByCodeAsync(ProjectCodeTextBox.Text.Trim().ToUpper());
                if (existingProject != null)
                {
                    MessageBox.Show("رمز المشروع موجود بالفعل، يرجى اختيار رمز آخر", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectCodeTextBox.Focus();
                    return;
                }

                // Create new project
                var newProject = new Project
                {
                    Name = ProjectNameTextBox.Text.Trim(),
                    Code = ProjectCodeTextBox.Text.Trim().ToUpper(),
                    PurchaseOrderAmount = poAmount,
                    Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ? null : DescriptionTextBox.Text.Trim(),
                    CreatedDate = DateTime.Now
                };

                await _projectService.AddAsync(newProject);

                MessageBox.Show("تم إضافة المشروع بنجاح", "نجح الحفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المشروع: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void PurchaseOrderAmountTextBox_PreviewTextInput(object sender, System.Windows.Input.TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            if (!char.IsDigit(e.Text, 0) && e.Text != ".")
            {
                e.Handled = true;
            }
        }
    }
}
