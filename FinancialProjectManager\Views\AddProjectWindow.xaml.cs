using System.Windows;
using FinancialProjectManager.Models;
using FinancialProjectManager.Services;

namespace FinancialProjectManager.Views
{
    public partial class AddProjectWindow : Window
    {
        private readonly ProjectService _projectService;

        public AddProjectWindow(ProjectService projectService)
        {
            InitializeComponent();
            _projectService = projectService;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(ProjectNameTextBox.Text))
                {
                    MessageBox.Show("Please enter project name", "Input Error",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(ProjectCodeTextBox.Text))
                {
                    MessageBox.Show("Please enter project code", "Input Error",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectCodeTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(PurchaseOrderAmountTextBox.Text, out decimal poAmount) || poAmount <= 0)
                {
                    MessageBox.Show("Please enter a valid purchase order amount", "Input Error",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    PurchaseOrderAmountTextBox.Focus();
                    return;
                }

                // Check if project code already exists
                var existingProject = await _projectService.GetByCodeAsync(ProjectCodeTextBox.Text.Trim().ToUpper());
                if (existingProject != null)
                {
                    MessageBox.Show("Project code already exists, please choose another code", "Input Error",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ProjectCodeTextBox.Focus();
                    return;
                }

                // Create new project
                var newProject = new Project
                {
                    Name = ProjectNameTextBox.Text.Trim(),
                    Code = ProjectCodeTextBox.Text.Trim().ToUpper(),
                    PurchaseOrderAmount = poAmount,
                    Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ? null : DescriptionTextBox.Text.Trim(),
                    CreatedDate = DateTime.Now
                };

                await _projectService.AddAsync(newProject);

                MessageBox.Show("Project added successfully", "Success",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving project: {ex.Message}", "Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void PurchaseOrderAmountTextBox_PreviewTextInput(object sender, System.Windows.Input.TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            if (!char.IsDigit(e.Text, 0) && e.Text != ".")
            {
                e.Handled = true;
            }
        }
    }
}
