using System.ComponentModel.DataAnnotations;

namespace FinancialProjectManager.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(10)]
        public string Code { get; set; } = string.Empty; // UMS, BNG, AA, NTP, HPBX
        
        [Required]
        public decimal PurchaseOrderAmount { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedDate { get; set; }
        
        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Commitment> Commitments { get; set; } = new List<Commitment>();
        public virtual ICollection<Letter> Letters { get; set; } = new List<Letter>();
        
        // Calculated properties
        public decimal TotalSpent => Invoices.Where(i => i.PaymentStatus == PaymentStatus.Paid || i.PaymentStatus == PaymentStatus.PartiallyPaid).Sum(i => i.AmountInEGP);
        
        public decimal RemainingAmount => PurchaseOrderAmount - TotalSpent;
        
        public decimal SpentPercentage => PurchaseOrderAmount > 0 ? (TotalSpent / PurchaseOrderAmount) * 100 : 0;
        
        public decimal HardwareSoftwareSpent => Invoices.Where(i => (i.InvoiceType == InvoiceType.Hardware || i.InvoiceType == InvoiceType.Software) && 
                                                                   (i.PaymentStatus == PaymentStatus.Paid || i.PaymentStatus == PaymentStatus.PartiallyPaid))
                                                       .Sum(i => i.AmountInEGP);
        
        public decimal ServicesSpent => Invoices.Where(i => i.InvoiceType == InvoiceType.Service && 
                                                           (i.PaymentStatus == PaymentStatus.Paid || i.PaymentStatus == PaymentStatus.PartiallyPaid))
                                               .Sum(i => i.AmountInEGP);
    }
}
