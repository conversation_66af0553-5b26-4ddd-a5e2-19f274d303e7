# نظام إدارة المشاريع المالية
## Financial Project Manager

تطبيق سطح مكتب شامل لإدارة العمليات المالية للمشاريع المتعددة باستخدام C# و WPF.

## المميزات الرئيسية

### 🏗️ إدارة المشاريع
- إدارة 5 مشاريع أساسية: UMS, BNG, AA, NTP, HPBX
- إمكانية إضافة مشاريع جديدة
- عرض ملخص مالي شامل لكل مشروع

### 💰 إدارة الفواتير
- تسجيل فواتير الأجهزة والبرمجيات والخدمات
- تحويل العملة من الدولار إلى الجنيه المصري
- رفع وحفظ ملفات PDF للفواتير
- تتبع حالة الدفع (معلق، مدفوع جزئياً، مدفوع، ملغي)

### 📋 إدارة الارتباطات
- تسجيل الارتباطات المالية
- ربط الارتباطات بالفواتير
- إدارة ملفات PDF للارتباطات

### 📄 إدارة الخطابات
- تسجيل الخطابات المرسلة للمشتريات
- حفظ ملفات PDF للخطابات
- تتبع تواريخ الإرسال

### 📊 الحسابات التلقائية
- حساب إجمالي المصروفات لكل مشروع
- حساب المبلغ المتبقي من أمر الشراء
- عرض نسبة الإنجاز المالي
- تحديث البيانات تلقائياً

## متطلبات التشغيل

### للتطوير:
- .NET 8.0 SDK
- Visual Studio 2022 أو VS Code
- Windows 10/11

### للاستخدام:
- Windows 10/11
- لا يتطلب تثبيت .NET (الملف التنفيذي مستقل)

## طرق التشغيل

### 1. التشغيل من الكود المصدري:
```bash
# الانتقال إلى مجلد المشروع
cd FinancialProjectManager

# تشغيل التطبيق
dotnet run
```

### 2. التشغيل من الملف التنفيذي المطور:
```bash
# تشغيل من مجلد Debug
.\bin\Debug\net8.0-windows\FinancialProjectManager.exe
```

### 3. التشغيل من الملف التنفيذي المستقل (الموصى به):
```bash
# الملف التنفيذي المستقل - لا يحتاج .NET مثبت
.\bin\Release\net8.0-windows\win-x64\publish\FinancialProjectManager.exe
```

## بناء الملف التنفيذي

### بناء عادي:
```bash
dotnet build -c Release
```

### بناء ملف تنفيذي مستقل:
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
```

## هيكل المشروع

```
FinancialProjectManager/
├── Models/              # نماذج البيانات
├── Data/               # قاعدة البيانات والسياق
├── Services/           # خدمات الأعمال
├── Views/              # واجهات المستخدم
├── ViewModels/         # نماذج العرض
├── Helpers/            # الأدوات المساعدة
└── bin/Release/        # الملفات التنفيذية
```

## قاعدة البيانات

- **النوع**: SQLite
- **الموقع**: `%USERPROFILE%\Documents\FinancialProjectManager\FinancialManager.db`
- **إنشاء تلقائي**: يتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- **البيانات الأولية**: يتم إدراج المشاريع الخمسة الأساسية تلقائياً

## ملفات PDF

- **مجلد التخزين**: `%USERPROFILE%\Documents\FinancialProjectManager\Files\`
- **التنظيم**: 
  - `Invoices/` - ملفات الفواتير
  - `Commitments/` - ملفات الارتباطات  
  - `Letters/` - ملفات الخطابات

## الواجهة

- **اللغة**: العربية مع دعم RTL
- **التصميم**: واجهة حديثة مع Material Design
- **الألوان**: نظام ألوان متناسق ومريح للعين

## الاستخدام

1. **تشغيل التطبيق**: انقر نقراً مزدوجاً على الملف التنفيذي
2. **عرض المشاريع**: ستظهر بطاقات المشاريع في الشاشة الرئيسية
3. **إضافة مشروع جديد**: انقر على "إضافة مشروع جديد"
4. **عرض تفاصيل مشروع**: انقر على "عرض التفاصيل" في بطاقة المشروع
5. **إدارة الفواتير**: استخدم تبويب "الفواتير" في صفحة تفاصيل المشروع
6. **رفع ملفات PDF**: انقر على "اختيار ملف" عند إضافة فاتورة/ارتباط/خطاب

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور خصيصاً لإدارة المشاريع المالية.
