using System.Windows;
using System.Windows.Controls;
using FinancialProjectManager.Models;
using FinancialProjectManager.Services;
using FinancialProjectManager.Data;

namespace FinancialProjectManager.Views
{
    public partial class ProjectDetailsWindow : Window
    {
        private readonly int _projectId;
        private readonly ProjectService _projectService;
        private readonly InvoiceService _invoiceService;
        private readonly FileService _fileService;
        private Project? _currentProject;

        public ProjectDetailsWindow(int projectId, ProjectService projectService, FileService fileService)
        {
            InitializeComponent();
            _projectId = projectId;
            _projectService = projectService;
            _fileService = fileService;
            
            var context = new FinancialDbContext();
            _invoiceService = new InvoiceService(context);
            
            LoadProjectDataAsync();
        }

        private async void LoadProjectDataAsync()
        {
            try
            {
                _currentProject = await _projectService.GetByIdAsync(_projectId);
                if (_currentProject == null)
                {
                    MessageBox.Show("لم يتم العثور على المشروع", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // Update header
                ProjectHeaderTextBlock.Text = $"تفاصيل المشروع: {_currentProject.Name} ({_currentProject.Code})";
                Title = $"تفاصيل المشروع: {_currentProject.Name}";

                // Update financial summary
                POAmountTextBlock.Text = $"{_currentProject.PurchaseOrderAmount:N0} جنيه";
                HardwareSoftwareSpentTextBlock.Text = $"{_currentProject.HardwareSoftwareSpent:N0} جنيه";
                ServicesSpentTextBlock.Text = $"{_currentProject.ServicesSpent:N0} جنيه";
                RemainingAmountTextBlock.Text = $"{_currentProject.RemainingAmount:N0} جنيه";

                // Load data grids
                LoadInvoicesData();
                LoadCommitmentsData();
                LoadLettersData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المشروع: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadInvoicesData()
        {
            if (_currentProject != null)
            {
                InvoicesDataGrid.ItemsSource = _currentProject.Invoices.OrderByDescending(i => i.ArrivalDate);
            }
        }

        private void LoadCommitmentsData()
        {
            if (_currentProject != null)
            {
                CommitmentsDataGrid.ItemsSource = _currentProject.Commitments.OrderByDescending(c => c.Date);
            }
        }

        private void LoadLettersData()
        {
            if (_currentProject != null)
            {
                LettersDataGrid.ItemsSource = _currentProject.Letters.OrderByDescending(l => l.Date);
            }
        }

        // Invoice event handlers
        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addInvoiceWindow = new AddInvoiceWindow(_projectId, _invoiceService, _fileService);
                if (addInvoiceWindow.ShowDialog() == true)
                {
                    LoadProjectDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الفاتورة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectDataAsync();
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int invoiceId)
            {
                try
                {
                    var editInvoiceWindow = new EditInvoiceWindow(invoiceId, _invoiceService, _fileService);
                    if (editInvoiceWindow.ShowDialog() == true)
                    {
                        LoadProjectDataAsync();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح نافذة تعديل الفاتورة: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int invoiceId)
            {
                try
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذه الفاتورة؟", "تأكيد الحذف", 
                                                MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        await _invoiceService.DeleteAsync(invoiceId);
                        LoadProjectDataAsync();
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OpenInvoiceFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string filePath && !string.IsNullOrEmpty(filePath))
            {
                if (!_fileService.OpenFile(filePath))
                {
                    MessageBox.Show("لا يمكن فتح الملف. تأكد من وجود الملف.", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                MessageBox.Show("لا يوجد ملف مرفق بهذه الفاتورة", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // Commitment event handlers
        private void AddCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshCommitmentsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectDataAsync();
        }

        private void EditCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OpenCommitmentFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string filePath && !string.IsNullOrEmpty(filePath))
            {
                if (!_fileService.OpenFile(filePath))
                {
                    MessageBox.Show("لا يمكن فتح الملف. تأكد من وجود الملف.", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                MessageBox.Show("لا يوجد ملف مرفق بهذا الارتباط", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ViewCommitmentInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Letter event handlers
        private void AddLetterButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshLettersButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectDataAsync();
        }

        private void EditLetterButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteLetterButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الوظيفة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OpenLetterFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string filePath && !string.IsNullOrEmpty(filePath))
            {
                if (!_fileService.OpenFile(filePath))
                {
                    MessageBox.Show("لا يمكن فتح الملف. تأكد من وجود الملف.", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                MessageBox.Show("لا يوجد ملف مرفق بهذا الخطاب", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
