using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialProjectManager.Models
{
    public class Commitment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string CommitmentNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime Date { get; set; }
        
        [Required]
        public CommitmentType CommitmentType { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        public SignatureStatus SignatureStatus { get; set; }
        
        public string? PdfFilePath { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedDate { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
        
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        
        public virtual ICollection<LetterCommitment> LetterCommitments { get; set; } = new List<LetterCommitment>();
        
        // Calculated properties
        public decimal TotalSpent => Invoices.Where(i => i.PaymentStatus == PaymentStatus.Paid || i.PaymentStatus == PaymentStatus.PartiallyPaid).Sum(i => i.AmountInEGP);
        
        public decimal RemainingAmount => Amount - TotalSpent;
        
        public decimal SpentPercentage => Amount > 0 ? (TotalSpent / Amount) * 100 : 0;
    }
    
    public enum CommitmentType
    {
        Hardware = 1,
        Software = 2,
        Service = 3
    }
    
    public enum SignatureStatus
    {
        NotSigned = 1,
        Signed = 2
    }
}
