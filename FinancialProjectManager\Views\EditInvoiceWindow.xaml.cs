using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using FinancialProjectManager.Models;
using FinancialProjectManager.Services;

namespace FinancialProjectManager.Views
{
    public partial class EditInvoiceWindow : Window
    {
        private readonly int _invoiceId;
        private readonly InvoiceService _invoiceService;
        private readonly FileService _fileService;
        private Invoice? _currentInvoice;
        private string? _selectedPdfPath;

        public EditInvoiceWindow(int invoiceId, InvoiceService invoiceService, FileService fileService)
        {
            InitializeComponent();
            _invoiceId = invoiceId;
            _invoiceService = invoiceService;
            _fileService = fileService;
            
            LoadInvoiceDataAsync();
        }

        private async void LoadInvoiceDataAsync()
        {
            try
            {
                _currentInvoice = await _invoiceService.GetByIdAsync(_invoiceId);
                if (_currentInvoice == null)
                {
                    MessageBox.Show("لم يتم العثور على الفاتورة", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // Populate form with current data
                InvoiceNumberTextBox.Text = _currentInvoice.InvoiceNumber;
                ArrivalDatePicker.SelectedDate = _currentInvoice.ArrivalDate;
                SignatureDatePicker.SelectedDate = _currentInvoice.SignatureDate;
                AmountInUSDTextBox.Text = _currentInvoice.AmountInUSD.ToString("N2");
                ExchangeRateTextBox.Text = _currentInvoice.ExchangeRate.ToString("N4");
                AmountInEGPTextBox.Text = _currentInvoice.AmountInEGP.ToString("N2");
                NotesTextBox.Text = _currentInvoice.Notes ?? "";

                // Set combo box selections
                foreach (ComboBoxItem item in InvoiceTypeComboBox.Items)
                {
                    if (item.Tag.ToString() == _currentInvoice.InvoiceType.ToString())
                    {
                        InvoiceTypeComboBox.SelectedItem = item;
                        break;
                    }
                }

                foreach (ComboBoxItem item in PaymentStatusComboBox.Items)
                {
                    if (item.Tag.ToString() == _currentInvoice.PaymentStatus.ToString())
                    {
                        PaymentStatusComboBox.SelectedItem = item;
                        break;
                    }
                }

                // Show current PDF file if exists
                if (!string.IsNullOrEmpty(_currentInvoice.PdfFilePath))
                {
                    PdfFilePathTextBox.Text = System.IO.Path.GetFileName(_currentInvoice.PdfFilePath);
                }

                Title = $"تعديل الفاتورة: {_currentInvoice.InvoiceNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الفاتورة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
            }
        }

        private void AmountInUSDTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateAmountInEGP();
        }

        private void ExchangeRateTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateAmountInEGP();
        }

        private void CalculateAmountInEGP()
        {
            if (decimal.TryParse(AmountInUSDTextBox.Text, out decimal usdAmount) &&
                decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
            {
                var egpAmount = usdAmount * exchangeRate;
                AmountInEGPTextBox.Text = egpAmount.ToString("N2");
            }
            else
            {
                AmountInEGPTextBox.Text = "0.00";
            }
        }

        private void SelectPdfFileButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف PDF",
                Filter = "PDF Files (*.pdf)|*.pdf",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedPdfPath = openFileDialog.FileName;
                PdfFilePathTextBox.Text = System.IO.Path.GetFileName(_selectedPdfPath);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentInvoice == null) return;

                // Validate input
                if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceNumberTextBox.Focus();
                    return;
                }

                if (!ArrivalDatePicker.SelectedDate.HasValue)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الوصول", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ArrivalDatePicker.Focus();
                    return;
                }

                if (!decimal.TryParse(AmountInUSDTextBox.Text, out decimal usdAmount) || usdAmount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح بالدولار", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    AmountInUSDTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate) || exchangeRate <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صرف صحيح", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ExchangeRateTextBox.Focus();
                    return;
                }

                if (InvoiceTypeComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الفاتورة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceTypeComboBox.Focus();
                    return;
                }

                if (PaymentStatusComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار حالة الصرف", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    PaymentStatusComboBox.Focus();
                    return;
                }

                // Check if invoice number is unique (excluding current invoice)
                var isUnique = await _invoiceService.IsInvoiceNumberUniqueAsync(InvoiceNumberTextBox.Text.Trim(), _invoiceId);
                if (!isUnique)
                {
                    MessageBox.Show("رقم الفاتورة موجود بالفعل، يرجى اختيار رقم آخر", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    InvoiceNumberTextBox.Focus();
                    return;
                }

                // Save new PDF file if selected
                string? savedPdfPath = _currentInvoice.PdfFilePath;
                if (!string.IsNullOrEmpty(_selectedPdfPath))
                {
                    savedPdfPath = await _fileService.SaveInvoiceFileAsync(InvoiceNumberTextBox.Text.Trim(), _selectedPdfPath);
                }

                // Parse enum values
                var invoiceTypeItem = (ComboBoxItem)InvoiceTypeComboBox.SelectedItem;
                var invoiceType = Enum.Parse<InvoiceType>(invoiceTypeItem.Tag.ToString()!);

                var paymentStatusItem = (ComboBoxItem)PaymentStatusComboBox.SelectedItem;
                var paymentStatus = Enum.Parse<PaymentStatus>(paymentStatusItem.Tag.ToString()!);

                // Update invoice
                _currentInvoice.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
                _currentInvoice.ArrivalDate = ArrivalDatePicker.SelectedDate.Value;
                _currentInvoice.SignatureDate = SignatureDatePicker.SelectedDate;
                _currentInvoice.AmountInUSD = usdAmount;
                _currentInvoice.ExchangeRate = exchangeRate;
                _currentInvoice.AmountInEGP = usdAmount * exchangeRate;
                _currentInvoice.InvoiceType = invoiceType;
                _currentInvoice.PaymentStatus = paymentStatus;
                _currentInvoice.Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim();
                _currentInvoice.PdfFilePath = savedPdfPath;
                _currentInvoice.LastModifiedDate = DateTime.Now;

                await _invoiceService.UpdateAsync(_currentInvoice);

                MessageBox.Show("تم تحديث الفاتورة بنجاح", "نجح الحفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
