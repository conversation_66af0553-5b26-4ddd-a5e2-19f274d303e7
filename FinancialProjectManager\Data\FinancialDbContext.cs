using Microsoft.EntityFrameworkCore;
using FinancialProjectManager.Models;
using System.IO;

namespace FinancialProjectManager.Data
{
    public class FinancialDbContext : DbContext
    {
        public DbSet<Project> Projects { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<Commitment> Commitments { get; set; }
        public DbSet<Letter> Letters { get; set; }
        public DbSet<LetterInvoice> LetterInvoices { get; set; }
        public DbSet<LetterCommitment> LetterCommitments { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var appDataPath = Path.Combine(documentsPath, "FinancialProjectManager");
                var dbPath = Path.Combine(appDataPath, "FinancialManager.db");

                if (!Directory.Exists(appDataPath))
                {
                    Directory.CreateDirectory(appDataPath);
                }

                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Project entity
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(10);
                entity.Property(e => e.PurchaseOrderAmount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.Code).IsUnique();
            });

            // Configure Invoice entity
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AmountInUSD).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ExchangeRate).HasColumnType("decimal(18,4)");
                entity.Property(e => e.AmountInEGP).HasColumnType("decimal(18,2)");
                
                entity.HasOne(e => e.Project)
                      .WithMany(p => p.Invoices)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(e => e.Commitment)
                      .WithMany(c => c.Invoices)
                      .HasForeignKey(e => e.CommitmentId)
                      .OnDelete(DeleteBehavior.SetNull);
                      
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
            });

            // Configure Commitment entity
            modelBuilder.Entity<Commitment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CommitmentNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                
                entity.HasOne(e => e.Project)
                      .WithMany(p => p.Commitments)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasIndex(e => e.CommitmentNumber).IsUnique();
            });

            // Configure Letter entity
            modelBuilder.Entity<Letter>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.LetterNumber).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(200);
                
                entity.HasOne(e => e.Project)
                      .WithMany(p => p.Letters)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure LetterInvoice junction table
            modelBuilder.Entity<LetterInvoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Letter)
                      .WithMany(l => l.LetterInvoices)
                      .HasForeignKey(e => e.LetterId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(e => e.Invoice)
                      .WithMany(i => i.LetterInvoices)
                      .HasForeignKey(e => e.InvoiceId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasIndex(e => new { e.LetterId, e.InvoiceId }).IsUnique();
            });

            // Configure LetterCommitment junction table
            modelBuilder.Entity<LetterCommitment>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Letter)
                      .WithMany(l => l.LetterCommitments)
                      .HasForeignKey(e => e.LetterId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(e => e.Commitment)
                      .WithMany(c => c.LetterCommitments)
                      .HasForeignKey(e => e.CommitmentId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasIndex(e => new { e.LetterId, e.CommitmentId }).IsUnique();
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed default projects
            modelBuilder.Entity<Project>().HasData(
                new Project { Id = 1, Name = "UMS Project", Code = "UMS", PurchaseOrderAmount = 1000000, Description = "UMS Project Description", CreatedDate = DateTime.Now },
                new Project { Id = 2, Name = "BNG Project", Code = "BNG", PurchaseOrderAmount = 800000, Description = "BNG Project Description", CreatedDate = DateTime.Now },
                new Project { Id = 3, Name = "AA Project", Code = "AA", PurchaseOrderAmount = 600000, Description = "AA Project Description", CreatedDate = DateTime.Now },
                new Project { Id = 4, Name = "NTP Project", Code = "NTP", PurchaseOrderAmount = 750000, Description = "NTP Project Description", CreatedDate = DateTime.Now },
                new Project { Id = 5, Name = "HPBX Project", Code = "HPBX", PurchaseOrderAmount = 900000, Description = "HPBX Project Description", CreatedDate = DateTime.Now }
            );
        }
    }
}
