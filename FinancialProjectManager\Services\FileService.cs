using System.Diagnostics;

namespace FinancialProjectManager.Services
{
    public class FileService
    {
        private readonly string _baseDataPath;
        private readonly string _invoicesPath;
        private readonly string _commitmentsPath;
        private readonly string _lettersPath;

        public FileService()
        {
            _baseDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            _invoicesPath = Path.Combine(_baseDataPath, "Invoices");
            _commitmentsPath = Path.Combine(_baseDataPath, "Commitments");
            _lettersPath = Path.Combine(_baseDataPath, "Letters");

            EnsureDirectoriesExist();
        }

        private void EnsureDirectoriesExist()
        {
            Directory.CreateDirectory(_baseDataPath);
            Directory.CreateDirectory(_invoicesPath);
            Directory.CreateDirectory(_commitmentsPath);
            Directory.CreateDirectory(_lettersPath);
        }

        public async Task<string?> SaveInvoiceFileAsync(string invoiceNumber, string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    return null;

                var fileName = $"{SanitizeFileName(invoiceNumber)}.pdf";
                var destinationPath = Path.Combine(_invoicesPath, fileName);

                await File.Copy(sourceFilePath, destinationPath, true);
                return destinationPath;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error saving invoice file: {ex.Message}");
                return null;
            }
        }

        public async Task<string?> SaveCommitmentFileAsync(string commitmentNumber, string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    return null;

                var fileName = $"{SanitizeFileName(commitmentNumber)}.pdf";
                var destinationPath = Path.Combine(_commitmentsPath, fileName);

                await File.Copy(sourceFilePath, destinationPath, true);
                return destinationPath;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error saving commitment file: {ex.Message}");
                return null;
            }
        }

        public async Task<string?> SaveLetterFileAsync(string letterNumber, string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    return null;

                var fileName = $"{SanitizeFileName(letterNumber)}.pdf";
                var destinationPath = Path.Combine(_lettersPath, fileName);

                await File.Copy(sourceFilePath, destinationPath, true);
                return destinationPath;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error saving letter file: {ex.Message}");
                return null;
            }
        }

        public bool OpenFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                Process.Start(new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });
                return true;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error opening file: {ex.Message}");
                return false;
            }
        }

        public bool DeleteFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error deleting file: {ex.Message}");
                return false;
            }
        }

        public bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }

        public string GetInvoicesDirectory() => _invoicesPath;
        public string GetCommitmentsDirectory() => _commitmentsPath;
        public string GetLettersDirectory() => _lettersPath;

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        public async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var dbPath = Path.Combine(_baseDataPath, "FinancialManager.db");
                if (File.Exists(dbPath))
                {
                    await File.Copy(dbPath, backupPath, true);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error backing up database: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                var dbPath = Path.Combine(_baseDataPath, "FinancialManager.db");
                if (File.Exists(backupPath))
                {
                    await File.Copy(backupPath, dbPath, true);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error restoring database: {ex.Message}");
                return false;
            }
        }
    }
}
