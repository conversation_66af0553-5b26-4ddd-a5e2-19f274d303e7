using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialProjectManager.Models
{
    public class Letter
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string LetterNumber { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string Subject { get; set; } = string.Empty;
        
        [Required]
        public DateTime Date { get; set; }
        
        public string? Content { get; set; }
        
        public string? PdfFilePath { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedDate { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
        
        public virtual ICollection<LetterInvoice> LetterInvoices { get; set; } = new List<LetterInvoice>();
        
        public virtual ICollection<LetterCommitment> LetterCommitments { get; set; } = new List<LetterCommitment>();
    }
    
    // Junction table for Letter-Invoice many-to-many relationship
    public class LetterInvoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int LetterId { get; set; }
        
        [Required]
        public int InvoiceId { get; set; }
        
        // Navigation properties
        [ForeignKey("LetterId")]
        public virtual Letter Letter { get; set; } = null!;
        
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;
    }
    
    // Junction table for Letter-Commitment many-to-many relationship
    public class LetterCommitment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int LetterId { get; set; }
        
        [Required]
        public int CommitmentId { get; set; }
        
        // Navigation properties
        [ForeignKey("LetterId")]
        public virtual Letter Letter { get; set; } = null!;
        
        [ForeignKey("CommitmentId")]
        public virtual Commitment Commitment { get; set; } = null!;
    }
}
