using Microsoft.EntityFrameworkCore;
using FinancialProjectManager.Data;
using FinancialProjectManager.Models;

namespace FinancialProjectManager.Services
{
    public class ProjectService : Repository<Project>
    {
        public ProjectService(FinancialDbContext context) : base(context)
        {
        }

        public override async Task<IEnumerable<Project>> GetAllAsync()
        {
            return await _dbSet
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .Include(p => p.Letters)
                .ToListAsync();
        }

        public override async Task<Project?> GetByIdAsync(int id)
        {
            return await _dbSet
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .Include(p => p.Letters)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project?> GetByCodeAsync(string code)
        {
            return await _dbSet
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .Include(p => p.Letters)
                .FirstOrDefaultAsync(p => p.Code == code);
        }

        public async Task<decimal> GetTotalPurchaseOrderAmountAsync()
        {
            return await _dbSet.SumAsync(p => p.PurchaseOrderAmount);
        }

        public async Task<decimal> GetTotalSpentAmountAsync()
        {
            var projects = await GetAllAsync();
            return projects.Sum(p => p.TotalSpent);
        }

        public async Task<decimal> GetTotalRemainingAmountAsync()
        {
            var projects = await GetAllAsync();
            return projects.Sum(p => p.RemainingAmount);
        }

        public async Task<Dictionary<string, decimal>> GetProjectSummaryAsync()
        {
            var projects = await GetAllAsync();
            return new Dictionary<string, decimal>
            {
                ["TotalPO"] = projects.Sum(p => p.PurchaseOrderAmount),
                ["TotalSpent"] = projects.Sum(p => p.TotalSpent),
                ["TotalRemaining"] = projects.Sum(p => p.RemainingAmount),
                ["ProjectCount"] = projects.Count()
            };
        }
    }
}
