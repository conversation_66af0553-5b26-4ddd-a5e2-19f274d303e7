﻿#pragma checksum "..\..\..\..\Views\ProjectDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B2E72659FAD8E2602472C77498B356A9DB22464C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialProjectManager.Views {
    
    
    /// <summary>
    /// ProjectDetailsWindow
    /// </summary>
    public partial class ProjectDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 66 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectHeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock POAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HardwareSoftwareSpentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServicesSpentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CommitmentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid LettersDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialProjectManager;component/views/projectdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectHeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.POAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HardwareSoftwareSpentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ServicesSpentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            
            #line 128 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 130 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            
            #line 180 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCommitmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 182 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshCommitmentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CommitmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 19:
            
            #line 232 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddLetterButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 234 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshLettersButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.LettersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 154 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 157 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 160 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenInvoiceFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 203 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 206 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 209 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenCommitmentFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 212 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCommitmentInvoicesButton_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 251 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditLetterButton_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 254 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteLetterButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 257 "..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenLetterFileButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

