﻿using System;
using System.Configuration;
using System.Data;
using System.Windows;
using FinancialProjectManager.Data;

namespace FinancialProjectManager;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // Initialize database
            using var context = new FinancialDbContext();
            context.Database.EnsureCreated();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }
}

