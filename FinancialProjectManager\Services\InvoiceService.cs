using Microsoft.EntityFrameworkCore;
using FinancialProjectManager.Data;
using FinancialProjectManager.Models;

namespace FinancialProjectManager.Services
{
    public class InvoiceService : Repository<Invoice>
    {
        public InvoiceService(FinancialDbContext context) : base(context)
        {
        }

        public override async Task<IEnumerable<Invoice>> GetAllAsync()
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.LetterInvoices)
                    .ThenInclude(li => li.Letter)
                .ToListAsync();
        }

        public override async Task<Invoice?> GetByIdAsync(int id)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.LetterInvoices)
                    .ThenInclude(li => li.Letter)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<IEnumerable<Invoice>> GetByProjectIdAsync(int projectId)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.LetterInvoices)
                    .ThenInclude(li => li.Letter)
                .Where(i => i.ProjectId == projectId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetByCommitmentIdAsync(int commitmentId)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.CommitmentId == commitmentId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> SearchAsync(string searchTerm)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.InvoiceNumber.Contains(searchTerm) ||
                           i.Notes!.Contains(searchTerm) ||
                           i.Project.Name.Contains(searchTerm))
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> FilterByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.ArrivalDate >= startDate && i.ArrivalDate <= endDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> FilterByStatusAsync(PaymentStatus status)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.PaymentStatus == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> FilterByTypeAsync(InvoiceType type)
        {
            return await _dbSet
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.InvoiceType == type)
                .ToListAsync();
        }

        public async Task<bool> IsInvoiceNumberUniqueAsync(string invoiceNumber, int? excludeId = null)
        {
            var query = _dbSet.Where(i => i.InvoiceNumber == invoiceNumber);
            if (excludeId.HasValue)
            {
                query = query.Where(i => i.Id != excludeId.Value);
            }
            return !await query.AnyAsync();
        }

        public async Task<Invoice> UpdateAmountInEGPAsync(int invoiceId, decimal exchangeRate)
        {
            var invoice = await GetByIdAsync(invoiceId);
            if (invoice != null)
            {
                invoice.ExchangeRate = exchangeRate;
                invoice.AmountInEGP = invoice.AmountInUSD * exchangeRate;
                invoice.LastModifiedDate = DateTime.Now;
                await UpdateAsync(invoice);
            }
            return invoice!;
        }
    }
}
